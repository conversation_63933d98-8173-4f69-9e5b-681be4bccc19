/**
 * 音频与输入系统节点注册表
 * 注册批次13：音频与输入系统节点（18个节点）到编辑器
 * 包括：音频系统节点(13个) + 输入系统节点(5个)
 */

import { NodeRegistry } from './NodeRegistry';

// 导入基础音频节点
import {
  LoadAudioNode,
  PlayAudioNode,
  SpatialAudioNode,
  AudioListenerNode
} from '../nodes/audio/AudioNodes';

// 导入高级音频节点
import {
  SpatialAudioNode as AdvancedSpatialAudioNode,
  AudioFilterNode,
  AudioEffectNode
} from '../nodes/audio/AdvancedAudioNodes';

// 导入音频系统节点
import {
  AudioMixerNode,
  AudioEffectChainNode,
  AudioReverbNode,
  AudioEQNode
} from '../nodes/audio/AdvancedAudioSystemNodes';

// 导入音频优化节点
import {
  AudioOptimizationNode,
  AudioStreamingNode
} from '../nodes/audio/AudioOptimizationNodes';

// 导入基础输入节点
import {
  KeyboardInputNode,
  MouseInputNode,
  TouchInputNode,
  GamepadInputNode
} from '../nodes/input/InputNodes';

// 导入高级输入节点
import {
  VoiceInputNode
} from '../nodes/input/AdvancedInputNodes';

/**
 * 音频与输入系统节点注册表类
 */
export class AudioInputNodesRegistry {
  private static instance: AudioInputNodesRegistry;
  private registered: boolean = false;
  private nodeRegistry: typeof NodeRegistry;

  constructor() {
    this.nodeRegistry = NodeRegistry;
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): AudioInputNodesRegistry {
    if (!AudioInputNodesRegistry.instance) {
      AudioInputNodesRegistry.instance = new AudioInputNodesRegistry();
    }
    return AudioInputNodesRegistry.instance;
  }

  /**
   * 注册所有音频与输入系统节点
   */
  public registerAllNodes(): void {
    if (this.registered) {
      console.log('音频与输入系统节点已经注册过了');
      return;
    }

    try {
      console.log('开始注册批次13：音频与输入系统节点...');

      // 注册基础音频节点（4个）
      this.registerBasicAudioNodes();

      // 注册高级音频节点（3个）
      this.registerAdvancedAudioNodes();

      // 注册音频系统节点（4个）
      this.registerAudioSystemNodes();

      // 注册音频优化节点（2个）
      this.registerAudioOptimizationNodes();

      // 注册基础输入节点（4个）
      this.registerBasicInputNodes();

      // 注册高级输入节点（1个）
      this.registerAdvancedInputNodes();

      this.registered = true;
      console.log('✅ 批次13音频与输入系统节点注册完成: 18个节点');
      console.log('  - 基础音频节点: 4个');
      console.log('  - 高级音频节点: 3个');
      console.log('  - 音频系统节点: 4个');
      console.log('  - 音频优化节点: 2个');
      console.log('  - 基础输入节点: 4个');
      console.log('  - 高级输入节点: 1个');

    } catch (error) {
      console.error('❌ 音频与输入系统节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册基础音频节点（4个）
   */
  private registerBasicAudioNodes(): void {
    console.log('注册基础音频节点...');

    // 音频加载节点
    this.nodeRegistry.registerNode('audio/loadAudio', LoadAudioNode, {
      category: '音频系统',
      description: '从URL加载音频文件',
      inputs: ['url', 'preload', 'loop'],
      outputs: ['audioId', 'duration', 'isLoaded', 'onLoad', 'onError']
    });

    // 音频播放节点
    this.nodeRegistry.registerNode('audio/playAudio', PlayAudioNode, {
      category: '音频系统',
      description: '播放音频文件',
      inputs: ['audioId', 'volume', 'pitch', 'loop', 'startTime', 'endTime'],
      outputs: ['playId', 'isPlaying', 'currentTime', 'onPlay', 'onStop', 'onEnd', 'onError']
    });

    // 空间音频节点
    this.nodeRegistry.registerNode('audio/spatialAudio', SpatialAudioNode, {
      category: '音频系统',
      description: '创建3D空间音频效果',
      inputs: ['audioId', 'entity', 'position', 'maxDistance', 'rolloffFactor'],
      outputs: ['playId', 'distance', 'volume', 'onPlay', 'onStop']
    });

    // 音频监听器节点
    this.nodeRegistry.registerNode('audio/audioListener', AudioListenerNode, {
      category: '音频系统',
      description: '设置3D音频监听器位置和方向',
      inputs: ['entity', 'position', 'orientation', 'masterVolume'],
      outputs: ['listenerPosition', 'listenerOrientation', 'currentVolume', 'onUpdate']
    });

    console.log('✅ 基础音频节点注册完成: 4个');
  }

  /**
   * 注册高级音频节点（3个）
   */
  private registerAdvancedAudioNodes(): void {
    console.log('注册高级音频节点...');

    // 音频滤波器节点
    this.nodeRegistry.registerNode('audio/audioFilter', AudioFilterNode, {
      category: '音频系统',
      description: '音频频率滤波处理',
      inputs: ['audioSource', 'filterType', 'frequency', 'Q', 'gain', 'enabled'],
      outputs: ['filteredAudio', 'filterConfig', 'onApply', 'onRemove']
    });

    // 音频效果节点
    this.nodeRegistry.registerNode('audio/audioEffect', AudioEffectNode, {
      category: '音频系统',
      description: '音频效果处理',
      inputs: ['audioSource', 'effectType', 'parameters', 'wetness', 'enabled'],
      outputs: ['processedAudio', 'effectConfig', 'onApply', 'onRemove']
    });

    console.log('✅ 高级音频节点注册完成: 3个');
  }

  /**
   * 注册音频系统节点（4个）
   */
  private registerAudioSystemNodes(): void {
    console.log('注册音频系统节点...');

    // 音频混合器节点
    this.nodeRegistry.registerNode('audio/audioMixer', AudioMixerNode, {
      category: '音频系统',
      description: '多通道音频混合和控制',
      inputs: ['audioSources', 'channelVolumes', 'masterVolume', 'crossfade'],
      outputs: ['mixedAudio', 'channelLevels', 'peakLevel', 'onMix']
    });

    // 音频效果链节点
    this.nodeRegistry.registerNode('audio/audioEffectChain', AudioEffectChainNode, {
      category: '音频系统',
      description: '串联多个音频效果处理',
      inputs: ['audioSource', 'effectChain', 'bypass', 'wetDryMix'],
      outputs: ['processedAudio', 'effectStates', 'onProcess']
    });

    // 音频混响节点
    this.nodeRegistry.registerNode('audio/audioReverb', AudioReverbNode, {
      category: '音频系统',
      description: '创建空间混响效果',
      inputs: ['audioSource', 'roomSize', 'damping', 'wetLevel', 'dryLevel'],
      outputs: ['reverbAudio', 'reverbConfig', 'onApply']
    });

    // 音频均衡器节点
    this.nodeRegistry.registerNode('audio/audioEQ', AudioEQNode, {
      category: '音频系统',
      description: '多频段音频均衡处理',
      inputs: ['audioSource', 'bands', 'presets', 'enabled'],
      outputs: ['equalizedAudio', 'bandLevels', 'onAdjust']
    });

    console.log('✅ 音频系统节点注册完成: 4个');
  }

  /**
   * 注册音频优化节点（2个）
   */
  private registerAudioOptimizationNodes(): void {
    console.log('注册音频优化节点...');

    // 音频优化节点
    this.nodeRegistry.registerNode('audio/audioOptimization', AudioOptimizationNode, {
      category: '音频系统',
      description: '配置和控制音频系统优化',
      inputs: ['maxSources', 'compressionLevel', 'qualityLevel', 'enableOptimization'],
      outputs: ['optimizationConfig', 'performanceMetrics', 'onOptimize']
    });

    // 音频流式传输节点
    this.nodeRegistry.registerNode('audio/audioStreaming', AudioStreamingNode, {
      category: '音频系统',
      description: '管理音频流式加载和播放',
      inputs: ['streamUrl', 'bufferSize', 'preloadTime', 'autoPlay'],
      outputs: ['streamId', 'bufferLevel', 'isStreaming', 'onStream', 'onBuffer']
    });

    console.log('✅ 音频优化节点注册完成: 2个');
  }

  /**
   * 注册基础输入节点（4个）
   */
  private registerBasicInputNodes(): void {
    console.log('注册基础输入节点...');

    // 键盘输入节点
    this.nodeRegistry.registerNode('input/keyboard', KeyboardInputNode, {
      category: '输入系统',
      description: '检测键盘按键输入',
      inputs: ['keyCode'],
      outputs: ['isPressed', 'isDown', 'isReleased', 'keyState', 'onPressed', 'onDown', 'onReleased']
    });

    // 鼠标输入节点
    this.nodeRegistry.registerNode('input/mouse', MouseInputNode, {
      category: '输入系统',
      description: '检测鼠标输入和位置',
      inputs: ['button'],
      outputs: ['position', 'deltaPosition', 'isPressed', 'isDown', 'isReleased', 'wheelDelta', 'onMove', 'onClick']
    });

    // 触摸输入节点
    this.nodeRegistry.registerNode('input/touch', TouchInputNode, {
      category: '输入系统',
      description: '检测触摸屏输入',
      inputs: ['touchIndex'],
      outputs: ['position', 'pressure', 'isActive', 'touchCount', 'onTouchStart', 'onTouchMove', 'onTouchEnd']
    });

    // 游戏手柄输入节点
    this.nodeRegistry.registerNode('input/gamepad', GamepadInputNode, {
      category: '输入系统',
      description: '检测游戏手柄输入',
      inputs: ['gamepadIndex', 'buttonIndex', 'axisIndex'],
      outputs: ['buttonState', 'axisValue', 'isConnected', 'onButtonPress', 'onAxisMove']
    });

    console.log('✅ 基础输入节点注册完成: 4个');
  }

  /**
   * 注册高级输入节点（1个）
   */
  private registerAdvancedInputNodes(): void {
    console.log('注册高级输入节点...');

    // 语音输入节点
    this.nodeRegistry.registerNode('input/voice', VoiceInputNode, {
      category: '输入系统',
      description: '处理语音识别和语音命令',
      inputs: ['language', 'continuous', 'commands', 'sensitivity'],
      outputs: ['recognizedText', 'confidence', 'isListening', 'matchedCommand', 'onRecognition', 'onCommand']
    });

    console.log('✅ 高级输入节点注册完成: 1个');
  }

  /**
   * 获取所有已注册的节点类型名称
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 基础音频节点（4个）
      'audio/loadAudio',
      'audio/playAudio',
      'audio/spatialAudio',
      'audio/audioListener',

      // 高级音频节点（3个）
      'audio/audioFilter',
      'audio/audioEffect',

      // 音频系统节点（4个）
      'audio/audioMixer',
      'audio/audioEffectChain',
      'audio/audioReverb',
      'audio/audioEQ',

      // 音频优化节点（2个）
      'audio/audioOptimization',
      'audio/audioStreaming',

      // 基础输入节点（4个）
      'input/keyboard',
      'input/mouse',
      'input/touch',
      'input/gamepad',

      // 高级输入节点（1个）
      'input/voice'
    ];
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取注册统计信息
   */
  public getRegistrationStats(): any {
    return {
      totalNodes: 18,
      basicAudioNodes: 4,
      advancedAudioNodes: 3,
      audioSystemNodes: 4,
      audioOptimizationNodes: 2,
      basicInputNodes: 4,
      advancedInputNodes: 1,
      registered: this.registered,
      registryName: 'AudioInputNodesRegistry'
    };
  }
}

// 导出单例实例
export const audioInputNodesRegistry = AudioInputNodesRegistry.getInstance();

// 自动注册（可选）
if (typeof window !== 'undefined') {
  // 在浏览器环境中自动注册
  audioInputNodesRegistry.registerAllNodes();
}
