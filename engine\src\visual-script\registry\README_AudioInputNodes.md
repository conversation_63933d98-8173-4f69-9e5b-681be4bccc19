# 音频与输入系统节点文档

## 概述

音频与输入系统节点注册表（AudioInputNodesRegistry）是DL引擎视觉脚本系统批次13的核心组件，提供了18个专业的音频处理和输入检测节点，涵盖从基础音频播放到高级音频效果处理，以及从简单键盘输入到复杂语音识别的完整功能集合。

## 节点分类

### 基础音频节点（4个）

#### 1. 音频加载节点 (LoadAudioNode)
- **类型**: `audio/loadAudio`
- **功能**: 从URL加载音频文件
- **输入**:
  - `url`: 音频文件URL
  - `preload`: 是否预加载
  - `loop`: 是否循环播放
- **输出**:
  - `audioId`: 音频ID
  - `duration`: 音频时长
  - `isLoaded`: 是否已加载
  - `onLoad`: 加载完成事件
  - `onError`: 加载错误事件

#### 2. 音频播放节点 (PlayAudioNode)
- **类型**: `audio/playAudio`
- **功能**: 播放音频文件
- **输入**:
  - `audioId`: 音频ID
  - `volume`: 音量 (0-1)
  - `pitch`: 音调 (0.5-2.0)
  - `loop`: 是否循环
  - `startTime`: 开始时间
  - `endTime`: 结束时间
- **输出**:
  - `playId`: 播放ID
  - `isPlaying`: 是否正在播放
  - `currentTime`: 当前播放时间
  - `onPlay`: 播放开始事件
  - `onStop`: 播放停止事件
  - `onEnd`: 播放结束事件
  - `onError`: 播放错误事件

#### 3. 空间音频节点 (SpatialAudioNode)
- **类型**: `audio/spatialAudio`
- **功能**: 创建3D空间音频效果
- **输入**:
  - `audioId`: 音频ID
  - `entity`: 关联实体
  - `position`: 3D位置
  - `maxDistance`: 最大距离
  - `rolloffFactor`: 衰减因子
- **输出**:
  - `playId`: 播放ID
  - `distance`: 距离监听器的距离
  - `volume`: 计算后的音量
  - `onPlay`: 播放事件
  - `onStop`: 停止事件

#### 4. 音频监听器节点 (AudioListenerNode)
- **类型**: `audio/audioListener`
- **功能**: 设置3D音频监听器位置和方向
- **输入**:
  - `entity`: 监听器实体
  - `position`: 监听器位置
  - `orientation`: 监听器方向
  - `masterVolume`: 主音量
- **输出**:
  - `listenerPosition`: 监听器位置
  - `listenerOrientation`: 监听器方向
  - `currentVolume`: 当前音量
  - `onUpdate`: 更新事件

### 高级音频节点（3个）

#### 5. 音频滤波器节点 (AudioFilterNode)
- **类型**: `audio/audioFilter`
- **功能**: 音频频率滤波处理
- **输入**:
  - `audioSource`: 音频源
  - `filterType`: 滤波器类型 (lowpass, highpass, bandpass等)
  - `frequency`: 截止频率
  - `Q`: 品质因子
  - `gain`: 增益
  - `enabled`: 是否启用
- **输出**:
  - `filteredAudio`: 滤波后的音频
  - `filterConfig`: 滤波器配置
  - `onApply`: 应用事件
  - `onRemove`: 移除事件

#### 6. 音频效果节点 (AudioEffectNode)
- **类型**: `audio/audioEffect`
- **功能**: 音频效果处理
- **输入**:
  - `audioSource`: 音频源
  - `effectType`: 效果类型 (reverb, delay, chorus等)
  - `parameters`: 效果参数
  - `wetness`: 湿度 (效果强度)
  - `enabled`: 是否启用
- **输出**:
  - `processedAudio`: 处理后的音频
  - `effectConfig`: 效果配置
  - `onApply`: 应用事件
  - `onRemove`: 移除事件

### 音频系统节点（4个）

#### 7. 音频混合器节点 (AudioMixerNode)
- **类型**: `audio/audioMixer`
- **功能**: 多通道音频混合和控制
- **输入**:
  - `audioSources`: 音频源数组
  - `channelVolumes`: 通道音量数组
  - `masterVolume`: 主音量
  - `crossfade`: 交叉淡化
- **输出**:
  - `mixedAudio`: 混合后的音频
  - `channelLevels`: 通道电平
  - `peakLevel`: 峰值电平
  - `onMix`: 混合事件

#### 8. 音频效果链节点 (AudioEffectChainNode)
- **类型**: `audio/audioEffectChain`
- **功能**: 串联多个音频效果处理
- **输入**:
  - `audioSource`: 音频源
  - `effectChain`: 效果链数组
  - `bypass`: 是否旁路
  - `wetDryMix`: 干湿混合比例
- **输出**:
  - `processedAudio`: 处理后的音频
  - `effectStates`: 效果状态
  - `onProcess`: 处理事件

#### 9. 音频混响节点 (AudioReverbNode)
- **类型**: `audio/audioReverb`
- **功能**: 创建空间混响效果
- **输入**:
  - `audioSource`: 音频源
  - `roomSize`: 房间大小
  - `damping`: 阻尼
  - `wetLevel`: 湿信号电平
  - `dryLevel`: 干信号电平
- **输出**:
  - `reverbAudio`: 混响音频
  - `reverbConfig`: 混响配置
  - `onApply`: 应用事件

#### 10. 音频均衡器节点 (AudioEQNode)
- **类型**: `audio/audioEQ`
- **功能**: 多频段音频均衡处理
- **输入**:
  - `audioSource`: 音频源
  - `bands`: 频段配置数组
  - `presets`: 预设名称
  - `enabled`: 是否启用
- **输出**:
  - `equalizedAudio`: 均衡后的音频
  - `bandLevels`: 频段电平
  - `onAdjust`: 调整事件

### 音频优化节点（2个）

#### 11. 音频优化节点 (AudioOptimizationNode)
- **类型**: `audio/audioOptimization`
- **功能**: 配置和控制音频系统优化
- **输入**:
  - `maxSources`: 最大音频源数量
  - `compressionLevel`: 压缩级别
  - `qualityLevel`: 质量级别
  - `enableOptimization`: 是否启用优化
- **输出**:
  - `optimizationConfig`: 优化配置
  - `performanceMetrics`: 性能指标
  - `onOptimize`: 优化事件

#### 12. 音频流式传输节点 (AudioStreamingNode)
- **类型**: `audio/audioStreaming`
- **功能**: 管理音频流式加载和播放
- **输入**:
  - `streamUrl`: 流媒体URL
  - `bufferSize`: 缓冲区大小
  - `preloadTime`: 预加载时间
  - `autoPlay`: 是否自动播放
- **输出**:
  - `streamId`: 流ID
  - `bufferLevel`: 缓冲区级别
  - `isStreaming`: 是否正在流式传输
  - `onStream`: 流事件
  - `onBuffer`: 缓冲事件

### 基础输入节点（4个）

#### 13. 键盘输入节点 (KeyboardInputNode)
- **类型**: `input/keyboard`
- **功能**: 检测键盘按键输入
- **输入**:
  - `keyCode`: 按键代码
- **输出**:
  - `isPressed`: 是否按下
  - `isDown`: 是否持续按下
  - `isReleased`: 是否释放
  - `keyState`: 按键状态
  - `onPressed`: 按下事件
  - `onDown`: 持续按下事件
  - `onReleased`: 释放事件

#### 14. 鼠标输入节点 (MouseInputNode)
- **类型**: `input/mouse`
- **功能**: 检测鼠标输入和位置
- **输入**:
  - `button`: 鼠标按钮 (left, right, middle)
- **输出**:
  - `position`: 鼠标位置
  - `deltaPosition`: 位置变化
  - `isPressed`: 是否按下
  - `isDown`: 是否持续按下
  - `isReleased`: 是否释放
  - `wheelDelta`: 滚轮变化
  - `onMove`: 移动事件
  - `onClick`: 点击事件

#### 15. 触摸输入节点 (TouchInputNode)
- **类型**: `input/touch`
- **功能**: 检测触摸屏输入
- **输入**:
  - `touchIndex`: 触摸点索引
- **输出**:
  - `position`: 触摸位置
  - `pressure`: 触摸压力
  - `isActive`: 是否活跃
  - `touchCount`: 触摸点数量
  - `onTouchStart`: 触摸开始事件
  - `onTouchMove`: 触摸移动事件
  - `onTouchEnd`: 触摸结束事件

#### 16. 游戏手柄输入节点 (GamepadInputNode)
- **类型**: `input/gamepad`
- **功能**: 检测游戏手柄输入
- **输入**:
  - `gamepadIndex`: 手柄索引
  - `buttonIndex`: 按钮索引
  - `axisIndex`: 轴索引
- **输出**:
  - `buttonState`: 按钮状态
  - `axisValue`: 轴值
  - `isConnected`: 是否连接
  - `onButtonPress`: 按钮按下事件
  - `onAxisMove`: 轴移动事件

### 高级输入节点（1个）

#### 17. 语音输入节点 (VoiceInputNode)
- **类型**: `input/voice`
- **功能**: 处理语音识别和语音命令
- **输入**:
  - `language`: 识别语言
  - `continuous`: 是否连续识别
  - `commands`: 命令列表
  - `sensitivity`: 敏感度
- **输出**:
  - `recognizedText`: 识别的文本
  - `confidence`: 置信度
  - `isListening`: 是否正在监听
  - `matchedCommand`: 匹配的命令
  - `onRecognition`: 识别事件
  - `onCommand`: 命令事件

## 使用示例

### 基础音频播放
```typescript
// 加载音频
const loadNode = nodeRegistry.createNode('audio/loadAudio');
const audioId = loadNode.execute({
  url: 'assets/audio/music.mp3',
  preload: true
}).audioId;

// 播放音频
const playNode = nodeRegistry.createNode('audio/playAudio');
playNode.execute({
  audioId: audioId,
  volume: 0.8,
  loop: true
});
```

### 3D空间音频
```typescript
// 设置监听器
const listenerNode = nodeRegistry.createNode('audio/audioListener');
listenerNode.execute({
  position: { x: 0, y: 0, z: 0 },
  orientation: { x: 0, y: 0, z: -1 }
});

// 创建空间音频
const spatialNode = nodeRegistry.createNode('audio/spatialAudio');
spatialNode.execute({
  audioId: audioId,
  position: { x: 10, y: 0, z: 5 },
  maxDistance: 50
});
```

### 键盘控制
```typescript
// 检测空格键
const keyboardNode = nodeRegistry.createNode('input/keyboard');
const result = keyboardNode.execute({ keyCode: 'Space' });

if (result.isPressed) {
  console.log('空格键被按下');
}
```

### 语音控制
```typescript
// 语音识别
const voiceNode = nodeRegistry.createNode('input/voice');
const result = voiceNode.execute({
  language: 'zh-CN',
  commands: ['播放', '暂停', '停止'],
  continuous: true
});

if (result.matchedCommand === '播放') {
  // 执行播放操作
}
```

## 应用场景

### 1. 游戏开发
- 背景音乐和音效管理
- 3D空间音频体验
- 多种输入设备支持
- 语音命令控制

### 2. 交互式应用
- 实时音频处理
- 手势和语音交互
- 多媒体内容播放
- 用户界面音效

### 3. 教育培训
- 语音识别评测
- 交互式音频课件
- 多感官学习体验
- 无障碍访问支持

### 4. 虚拟现实
- 沉浸式音频环境
- 空间定位音效
- 自然交互方式
- 实时音频反馈

## 技术特性

### 音频处理
- 支持多种音频格式
- 实时音频效果处理
- 3D空间音频定位
- 高质量音频流式传输

### 输入检测
- 多设备输入支持
- 实时输入状态监控
- 手势和语音识别
- 自定义输入映射

### 性能优化
- 音频资源管理
- 延迟优化
- 内存使用优化
- 批处理支持

### 跨平台兼容
- Web Audio API支持
- 移动设备适配
- 桌面应用兼容
- 浏览器兼容性

## 最佳实践

### 音频管理
1. 合理设置音频缓冲区大小
2. 使用音频池管理音频资源
3. 及时释放不用的音频对象
4. 根据设备性能调整音质

### 输入处理
1. 避免频繁的输入状态检查
2. 使用事件驱动的输入处理
3. 合理设置输入敏感度
4. 提供输入设备回退方案

### 性能优化
1. 使用音频压缩减少带宽
2. 实现音频预加载策略
3. 优化音频效果链长度
4. 监控音频系统性能

## 故障排除

### 常见问题
1. **音频无法播放**: 检查音频文件格式和路径
2. **空间音频效果不明显**: 调整监听器和音源位置
3. **输入检测不准确**: 检查输入设备连接和权限
4. **语音识别失败**: 确认麦克风权限和网络连接

### 调试技巧
1. 使用浏览器开发者工具检查音频状态
2. 监控音频上下文状态
3. 检查输入事件绑定
4. 验证节点连接和数据流

## 版本信息

- **版本**: 1.0.0
- **发布日期**: 2025年7月7日
- **兼容性**: DL引擎 v2.0+
- **依赖**: Web Audio API, 输入设备API

## 许可证

本节点库遵循DL引擎的许可证协议。
