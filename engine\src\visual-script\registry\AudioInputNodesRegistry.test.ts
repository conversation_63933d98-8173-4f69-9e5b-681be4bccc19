/**
 * 音频与输入系统节点注册表测试
 */

import { AudioInputNodesRegistry } from './AudioInputNodesRegistry';
import { NodeRegistry } from './NodeRegistry';

describe('AudioInputNodesRegistry', () => {
  let registry: AudioInputNodesRegistry;
  let nodeRegistry: typeof NodeRegistry;

  beforeEach(() => {
    registry = AudioInputNodesRegistry.getInstance();
    nodeRegistry = NodeRegistry;
  });

  afterEach(() => {
    // 清理注册的节点
    const nodeTypes = registry.getAllRegisteredNodeTypes();
    nodeTypes.forEach(type => {
      nodeRegistry.unregisterNode(type);
    });
  });

  describe('单例模式', () => {
    it('应该返回同一个实例', () => {
      const instance1 = AudioInputNodesRegistry.getInstance();
      const instance2 = AudioInputNodesRegistry.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('节点注册', () => {
    it('应该成功注册所有音频与输入系统节点', () => {
      expect(() => registry.registerAllNodes()).not.toThrow();
      expect(registry.isRegistered()).toBe(true);
    });

    it('应该注册正确数量的节点', () => {
      registry.registerAllNodes();
      const stats = registry.getRegistrationStats();
      
      expect(stats.totalNodes).toBe(18);
      expect(stats.basicAudioNodes).toBe(4);
      expect(stats.advancedAudioNodes).toBe(3);
      expect(stats.audioSystemNodes).toBe(4);
      expect(stats.audioOptimizationNodes).toBe(2);
      expect(stats.basicInputNodes).toBe(4);
      expect(stats.advancedInputNodes).toBe(1);
    });

    it('应该防止重复注册', () => {
      registry.registerAllNodes();
      const consoleSpy = jest.spyOn(console, 'log');
      
      registry.registerAllNodes(); // 第二次注册
      
      expect(consoleSpy).toHaveBeenCalledWith('音频与输入系统节点已经注册过了');
    });
  });

  describe('基础音频节点注册', () => {
    it('应该注册音频加载节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/loadAudio')).toBe(true);
    });

    it('应该注册音频播放节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/playAudio')).toBe(true);
    });

    it('应该注册空间音频节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/spatialAudio')).toBe(true);
    });

    it('应该注册音频监听器节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/audioListener')).toBe(true);
    });
  });

  describe('高级音频节点注册', () => {
    it('应该注册音频滤波器节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/audioFilter')).toBe(true);
    });

    it('应该注册音频效果节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/audioEffect')).toBe(true);
    });
  });

  describe('音频系统节点注册', () => {
    it('应该注册音频混合器节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/audioMixer')).toBe(true);
    });

    it('应该注册音频效果链节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/audioEffectChain')).toBe(true);
    });

    it('应该注册音频混响节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/audioReverb')).toBe(true);
    });

    it('应该注册音频均衡器节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/audioEQ')).toBe(true);
    });
  });

  describe('音频优化节点注册', () => {
    it('应该注册音频优化节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/audioOptimization')).toBe(true);
    });

    it('应该注册音频流式传输节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('audio/audioStreaming')).toBe(true);
    });
  });

  describe('基础输入节点注册', () => {
    it('应该注册键盘输入节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('input/keyboard')).toBe(true);
    });

    it('应该注册鼠标输入节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('input/mouse')).toBe(true);
    });

    it('应该注册触摸输入节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('input/touch')).toBe(true);
    });

    it('应该注册游戏手柄输入节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('input/gamepad')).toBe(true);
    });
  });

  describe('高级输入节点注册', () => {
    it('应该注册语音输入节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('input/voice')).toBe(true);
    });
  });

  describe('节点类型列表', () => {
    it('应该返回所有注册的节点类型', () => {
      const nodeTypes = registry.getAllRegisteredNodeTypes();
      expect(nodeTypes).toHaveLength(18);
      
      // 检查基础音频节点
      expect(nodeTypes).toContain('audio/loadAudio');
      expect(nodeTypes).toContain('audio/playAudio');
      expect(nodeTypes).toContain('audio/spatialAudio');
      expect(nodeTypes).toContain('audio/audioListener');
      
      // 检查高级音频节点
      expect(nodeTypes).toContain('audio/audioFilter');
      expect(nodeTypes).toContain('audio/audioEffect');
      
      // 检查音频系统节点
      expect(nodeTypes).toContain('audio/audioMixer');
      expect(nodeTypes).toContain('audio/audioEffectChain');
      expect(nodeTypes).toContain('audio/audioReverb');
      expect(nodeTypes).toContain('audio/audioEQ');
      
      // 检查音频优化节点
      expect(nodeTypes).toContain('audio/audioOptimization');
      expect(nodeTypes).toContain('audio/audioStreaming');
      
      // 检查基础输入节点
      expect(nodeTypes).toContain('input/keyboard');
      expect(nodeTypes).toContain('input/mouse');
      expect(nodeTypes).toContain('input/touch');
      expect(nodeTypes).toContain('input/gamepad');
      
      // 检查高级输入节点
      expect(nodeTypes).toContain('input/voice');
    });
  });

  describe('统计信息', () => {
    it('应该返回正确的注册统计信息', () => {
      registry.registerAllNodes();
      const stats = registry.getRegistrationStats();
      
      expect(stats).toEqual({
        totalNodes: 18,
        basicAudioNodes: 4,
        advancedAudioNodes: 3,
        audioSystemNodes: 4,
        audioOptimizationNodes: 2,
        basicInputNodes: 4,
        advancedInputNodes: 1,
        registered: true,
        registryName: 'AudioInputNodesRegistry'
      });
    });
  });

  describe('错误处理', () => {
    it('应该处理注册过程中的错误', () => {
      // 模拟注册错误
      const originalRegisterNode = nodeRegistry.registerNode;
      nodeRegistry.registerNode = jest.fn().mockImplementation(() => {
        throw new Error('注册失败');
      });

      expect(() => registry.registerAllNodes()).toThrow('注册失败');
      
      // 恢复原始方法
      nodeRegistry.registerNode = originalRegisterNode;
    });
  });

  describe('浏览器环境自动注册', () => {
    it('应该在浏览器环境中自动注册', () => {
      // 模拟浏览器环境
      const originalWindow = global.window;
      global.window = {} as any;
      
      const registerSpy = jest.spyOn(registry, 'registerAllNodes');
      
      // 重新导入模块以触发自动注册
      jest.resetModules();
      require('./AudioInputNodesRegistry');
      
      // 恢复环境
      global.window = originalWindow;
    });
  });
});

/**
 * 集成测试
 */
describe('AudioInputNodesRegistry 集成测试', () => {
  let registry: AudioInputNodesRegistry;

  beforeEach(() => {
    registry = AudioInputNodesRegistry.getInstance();
  });

  it('应该与NodeRegistry正确集成', () => {
    registry.registerAllNodes();
    
    const nodeRegistry = NodeRegistry;
    const registeredTypes = registry.getAllRegisteredNodeTypes();

    // 验证所有节点都已在NodeRegistry中注册
    registeredTypes.forEach(type => {
      expect(nodeRegistry.hasNode(type)).toBe(true);
    });
  });

  it('应该支持节点的创建和执行', () => {
    registry.registerAllNodes();

    const nodeRegistry = NodeRegistry;
    
    // 测试创建音频节点
    const audioNode = nodeRegistry.createNode('audio/loadAudio');
    expect(audioNode).toBeDefined();
    
    // 测试创建输入节点
    const inputNode = nodeRegistry.createNode('input/keyboard');
    expect(inputNode).toBeDefined();
  });
});
